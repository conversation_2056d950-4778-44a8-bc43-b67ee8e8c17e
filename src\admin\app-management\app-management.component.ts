import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { AppManagementService } from '../../services/app-management.service';
import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { PageTitleService } from '../../Utils/_services/page-title.service';

declare var bootstrap: any;

@Component({
  selector: 'app-app-management',
  standalone: true,
  imports: [FormsModule, NgFor, NgIf],
  templateUrl: './app-management.component.html',
  styleUrl: './app-management.component.css'
})
export class AppManagementComponent {
  apps: any[] = [];
  pageNumber = 1;
  pageSize = 5;
  totalCount = 0;
  totalPages = 0;

  isEditMode = false;
  currentApp: any = {
    id: null,
    title: '',
    description: '',
    logoUrl: '',
    websiteUrl: '',
    storeUrl: ''
  };

  constructor(
    private appService: AppManagementService,
    private pageTitle: PageTitleService
  ) {}

  ngOnInit(): void {
    this.pageTitle.setTitle('App Management');
    this.loadApps(this.pageNumber);
  }

  loadApps(page: number): void {
    this.pageNumber = page;
    const params = { pageNumber: this.pageNumber, pageSize: this.pageSize };

    this.appService.getApps(params).subscribe({
      next: (response) => {
        this.apps = response.items;
        this.totalCount = response.totalCount;
        this.totalPages = this.totalCount && this.pageSize
          ? Math.ceil(this.totalCount / this.pageSize)
          : 1;
      },
      error: (err) => {
        console.error('Failed to load apps:', err);
        alert('Error loading apps. Please try again.');
      }
    });
  }

  totalPagesArray(): number[] {
    return Array.from({ length: this.totalPages }, (_, i) => i + 1);
  }

  openAddAppModal(): void {
    this.isEditMode = false;
    this.resetCurrentApp();
    const modal = new bootstrap.Modal(document.getElementById('appModal')!);
    modal.show();
  }

  openEditAppModal(app: any): void {
    this.isEditMode = true;
    this.currentApp = { ...app };
    const modal = new bootstrap.Modal(document.getElementById('appModal')!);
    modal.show();
  }

  saveApp(): void {
    if (this.isEditMode) {
      this.updateApp();
    } else {
      this.addApp();
    }
  }

  addApp(): void {
    this.appService.createApp(this.currentApp).subscribe({
      next: () => {
        this.closeModal();
        this.loadApps(this.pageNumber);
      },
      error: (err) => {
        console.error('Add app failed:', err);
        alert('Failed to add app: ' + err.message);
      }
    });
  }

  updateApp(): void {
    this.appService.updateApp(this.currentApp.id, this.currentApp).subscribe({
      next: () => {
        this.closeModal();
        this.loadApps(this.pageNumber);
      },
      error: (err) => {
        console.error('Update app failed:', err);
        alert('Failed to update app: ' + err.message);
      }
    });
  }

  deleteApp(id: string): void {
    if (confirm('Are you sure you want to delete this app?')) {
      this.appService.deleteApp(id).subscribe({
        next: () => this.loadApps(this.pageNumber),
        error: (err) => {
          console.error('Delete app failed:', err);
          alert('Failed to delete app: ' + err.message);
        }
      });
    }
  }

  closeModal(): void {
    const modalElement = document.getElementById('appModal')!;
    const modalInstance = bootstrap.Modal.getInstance(modalElement);
    modalInstance?.hide();
    this.resetCurrentApp();
  }

  resetCurrentApp(): void {
    this.currentApp = {
      id: null,
      title: '',
      description: '',
      logoUrl: '',
      websiteUrl: '',
      storeUrl: ''
    };
  }
}
