<div class="about-container">
  <!-- Hero Section -->
  <section class="about-hero">
    <div class="hero-background">
      <div class="floating-elements">
        <div class="floating-circle circle-1"></div>
        <div class="floating-circle circle-2"></div>
        <div class="floating-circle circle-3"></div>
        <div class="floating-circle circle-4"></div>
      </div>
    </div>
    
    <div class="hero-content">
      <div class="hero-badge">
        <span class="badge-icon">🏢</span>
        <span>About Insizon</span>
      </div>
      
      <h1 class="hero-title">
        <span class="title-word">About</span>
        <span class="title-word highlight">Us</span>
      </h1>
      
      <p class="hero-description">
        We are a technology-driven company committed to building impactful software solutions 
        that solve real-world problems and transform businesses.
      </p>
      
      <div class="hero-stats">
        <div class="stat-item" *ngFor="let stat of stats">
          <div class="stat-number">{{stat.number}}</div>
          <div class="stat-label">{{stat.label}}</div>
        </div>
      </div>
    </div>
  </section>

  <!-- Main Content -->
  <section class="about-content">
    <div class="content-container">
      
      <!-- Mission & Vision Cards -->
      <div class="mission-vision-grid">
        <div class="about-card mission-card">
          <div class="card-header">
            <div class="card-icon">🎯</div>
            <h2 class="card-title">Our Mission</h2>
          </div>
          <div class="card-content">
            <p class="card-description">
              To empower businesses and individuals with cutting-edge, user-focused digital solutions 
              that drive growth and innovation in the modern world.
            </p>
          </div>
          <div class="card-decoration">
            <div class="decoration-line"></div>
          </div>
        </div>

        <div class="about-card vision-card">
          <div class="card-header">
            <div class="card-icon">🚀</div>
            <h2 class="card-title">Our Vision</h2>
          </div>
          <div class="card-content">
            <p class="card-description">
              To be a global leader in software innovation and customer satisfaction, 
              setting new standards for excellence in technology solutions.
            </p>
          </div>
          <div class="card-decoration">
            <div class="decoration-line"></div>
          </div>
        </div>
      </div>

      <!-- Values Section -->
      <div class="values-section">
        <div class="section-header">
          <h2 class="section-title">Our Core Values</h2>
          <div class="section-underline"></div>
          <p class="section-subtitle">
            The principles that guide everything we do
          </p>
        </div>

        <div class="values-grid">
          <div class="value-card" *ngFor="let value of values; let i = index">
            <div class="value-icon-wrapper">
              <div class="value-icon">{{value.icon}}</div>
              <div class="icon-glow"></div>
            </div>
            <div class="value-content">
              <h3 class="value-title">{{value.title}}</h3>
              <p class="value-description">{{value.description}}</p>
            </div>
            <div class="value-number">{{i + 1}}</div>
          </div>
        </div>
      </div>

      <!-- Company Story Section -->
      <div class="story-section">
        <div class="story-content">
          <div class="story-text">
            <h2 class="story-title">Our Story</h2>
            <div class="story-underline"></div>
            <p class="story-description">
              Founded with a vision to bridge the gap between technology and human needs, 
              Insizon has grown from a small startup to a trusted partner for businesses 
              worldwide. Our journey is marked by continuous innovation, unwavering commitment 
              to quality, and a passion for creating solutions that make a difference.
            </p>
            <div class="story-highlights">
              <div class="highlight-item">
                <span class="highlight-icon">⭐</span>
                <span>Award-winning solutions</span>
              </div>
              <div class="highlight-item">
                <span class="highlight-icon">🌍</span>
                <span>Global reach</span>
              </div>
              <div class="highlight-item">
                <span class="highlight-icon">🔒</span>
                <span>Enterprise security</span>
              </div>
            </div>
          </div>
          <div class="story-visual">
            <div class="visual-container">
              <div class="visual-circle main-circle">
                <div class="circle-content">
                  <span class="circle-icon">🏢</span>
                  <span class="circle-text">Insizon</span>
                </div>
              </div>
              <div class="visual-circle orbit-circle orbit-1">
                <span class="orbit-icon">💡</span>
              </div>
              <div class="visual-circle orbit-circle orbit-2">
                <span class="orbit-icon">🚀</span>
              </div>
              <div class="visual-circle orbit-circle orbit-3">
                <span class="orbit-icon">⭐</span>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </section>

  <!-- Call to Action -->
  <section class="cta-section">
    <div class="cta-content">
      <h2 class="cta-title">Ready to Work With Us?</h2>
      <p class="cta-description">
        Let's build something amazing together
      </p>
      <div class="cta-buttons">
        <button class="cta-button primary">
          <span class="button-icon">📞</span>
          <span>Contact Us</span>
        </button>
        <button class="cta-button secondary">
          <span class="button-icon">💼</span>
          <span>View Projects</span>
        </button>
      </div>
    </div>
  </section>
</div>
