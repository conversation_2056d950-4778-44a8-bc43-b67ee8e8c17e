<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold">Job Posts</h2>
    <button
      class="btn btn-primary"
      data-bs-toggle="modal"
      data-bs-target="#jobModal"
      (click)="openModal('create')">
      Create Job
    </button>
  </div>

  <!-- List of Posted Jobs -->
  <div class="mt-4" *ngIf="jobs.length > 0; else noJobs">
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
      <div class="col" *ngFor="let job of jobs; let i = index">
        <div class="card shadow-sm h-100 border-0">
          <div class="card-body">
            <h5 class="card-title fw-bold">{{ job.jobTitle }}</h5>
            <p class="card-text mb-2">{{ job.description }}</p>
            <ul class="list-unstyled mb-3">
              <li><strong>Type:</strong> {{ job.jobType }}</li>
              <li><strong>Mode:</strong> {{ job.mode }}</li>
              <li *ngIf="job.location"><strong>Location:</strong> {{ job.location }}</li>
              <li><strong>Salary:</strong> ₹{{ job.salaryPackage }}</li>
              <li *ngIf="job.requirements"><strong>Requirements:</strong> {{ job.requirements }}</li>
            </ul>
            <button
              class="btn btn-outline-primary btn-sm me-2"
              data-bs-toggle="modal"
              data-bs-target="#jobModal"
              (click)="openModal('edit', job)">
              <i class="bi bi-pencil-square"></i> Edit
            </button>
            <button
              class="btn btn-outline-danger btn-sm"
              (click)="deleteJob(i, job.jobId)">
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-template #noJobs>
    <div class="alert alert-info mt-4 text-center">
      <i class="bi bi-info-circle me-2"></i> No jobs posted yet. Click
      <strong>"Create Job"</strong> to add one.
    </div>
  </ng-template>
</div>

<!-- Create/Edit Job Modal -->
<div
  class="modal fade"
  id="jobModal"
  tabindex="-1"
  aria-labelledby="jobModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content shadow">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="jobModalLabel">
          {{ modalMode === 'create' ? 'Create New Job' : 'Edit Job' }}
        </h5>
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
          aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form
          (ngSubmit)="modalMode === 'create' ? postJob() : updateJob()"
          class="row g-3"
          #jobForm="ngForm">
          <div class="col-md-6">
            <label class="form-label fw-semibold">Job Title</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.jobTitle"
              name="jobTitle"
              required />
          </div>

          <div class="col-md-6">
            <label class="form-label fw-semibold">Company</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.company"
              name="company"
              required />
          </div>

          <div class="col-md-6">
            <label class="form-label fw-semibold">Job Type</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.jobType"
              name="jobType"
              required />
          </div>

          <div class="col-md-6">
            <label class="form-label fw-semibold">Mode</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.mode"
              name="mode"
              required />
          </div>

          <div class="col-md-6">
            <label class="form-label fw-semibold">Location</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.location"
              name="location"
              required />
          </div>

          <div class="col-md-6">
            <label class="form-label fw-semibold">Salary</label>
            <input
              type="number"
              class="form-control"
              [(ngModel)]="job.salaryPackage"
              name="salaryPackage"
              required />
          </div>

          <div class="col-12">
            <label class="form-label fw-semibold">Requirements</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.requirements"
              name="requirements"
              required />
          </div>

          <div class="col-12">
            <label class="form-label fw-semibold">Description</label>
            <textarea
              class="form-control"
              rows="4"
              [(ngModel)]="job.description"
              name="description"
              required></textarea>
          </div>

          <div class="col-12 text-end">
            <button
              type="submit"
              class="btn btn-success"
              [disabled]="!jobForm.form.valid">
              {{ modalMode === 'create' ? 'Post Job' : 'Update Job' }}
            </button>
            <button
              type="button"
              class="btn btn-secondary ms-2"
              (click)="resetForm()">
              Reset
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
