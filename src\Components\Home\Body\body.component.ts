import { Component, OnInit } from '@angular/core';
import { AccountService } from "../../../Utils/_services/account.service";
import { FormsModule } from '@angular/forms';
import { NgIf, AsyncPipe, CommonModule, TitleCasePipe, NgFor } from '@angular/common';
import { Observable, of } from 'rxjs';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { AppManagementService } from '../../../services/app-management.service';


@Component({
  selector: 'body-content-item',
  standalone: true,
  imports: [NgFor],
  templateUrl: './body.component.html',
  styleUrl: './body.component.css'
})
export class BodyContentItemComponent implements OnInit {

  apps: any[] = [];
  groupedApps: any[][] = [];

  constructor(private appService: AppManagementService) {}

  ngOnInit(): void {
    this.loadApps();
  }

  loadApps(): void {
    this.appService.getAllApps().subscribe({
      next: (response) => {
        this.apps = response;
        this.groupAppsForCarousel();
      },
      error: (err) => {
        console.error('Failed to load apps:', err);
        alert('Error loading apps. Please try again.');
      }
    });
  }

  groupAppsForCarousel(): void {
    const groupSize = 3; // 3 items per carousel slide
    for (let i = 0; i < this.apps.length; i += groupSize) {
      this.groupedApps.push(this.apps.slice(i, i + groupSize));
    }
  }

openWebsite(url: string | null): void {
    if (url) {
      window.open(url, '_blank');
    }
  }
  visitMaxgeneye() {
    window.open("https://maxgeneye.com/", "_blank")
  }
}
