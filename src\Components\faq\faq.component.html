<div class="faq-page">
  <!-- Hero Section -->
  <section class="faq-hero">
    <div class="hero-background">
      <div class="floating-elements">
        <div class="floating-circle circle-1"></div>
        <div class="floating-circle circle-2"></div>
        <div class="floating-circle circle-3"></div>
      </div>
    </div>
    
    <div class="hero-content">
      <div class="hero-badge">
        <span class="badge-icon">❓</span>
        <span>Help Center</span>
      </div>
      
      <h1 class="hero-title">
        <span class="title-word">Frequently</span>
        <span class="title-word">Asked</span>
        <span class="title-word highlight">Questions</span>
      </h1>
      
      <p class="hero-description">
        Find answers to common questions about our services and policies
      </p>
    </div>
  </section>

  <!-- FAQ Content -->
  <div class="faq-content">
    <div class="content-container">
      <div class="faq-container">
        <div class="faq-header">
          <h2 class="section-title">Got Questions? We've Got Answers</h2>
          <p class="section-subtitle">Everything you need to know about our platform</p>
        </div>

        <div class="faq-list">
          <div class="faq-item" *ngFor="let faq of faqs; let i = index">
            <div class="faq-card">
              <div class="faq-number">
                <span>{{i + 1}}</span>
              </div>
              <div class="faq-content-wrapper">
                <div class="faq-question">
                  <h3>{{faq.question}}</h3>
                  <div class="question-icon">
                    <span>💡</span>
                  </div>
                </div>
                <div class="faq-answer">
                  <p>{{faq.answer}}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Support -->
        <div class="support-section">
          <div class="support-card">
            <div class="support-icon">
              <span>🤝</span>
            </div>
            <div class="support-content">
              <h3>Still have questions?</h3>
              <p>Can't find the answer you're looking for? Please chat with our friendly team.</p>
              <button class="support-btn">
                <span class="btn-icon">📧</span>
                <span>Contact Support</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
