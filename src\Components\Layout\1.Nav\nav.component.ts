import { Component, HostListener, OnInit } from '@angular/core';
import { AccountService } from "../../../Utils/_services/account.service";
import { FormsModule } from '@angular/forms';
import { NgIf, AsyncPipe, CommonModule, TitleCasePipe } from '@angular/common';
import { Observable, of } from 'rxjs';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { BodyContentItemComponent } from "../../Home/Body/body.component";


@Component({
  selector: 'app-nav',
  standalone: true,
  imports: [FormsModule, CommonModule, RouterLink],
  templateUrl: './nav.component.html',
  styleUrl: './nav.component.css'
})
export class NavComponent implements OnInit {


  constructor() {
  }

  ngOnInit(): void {
  }
 isScrolled = false
  isMobileMenuOpen = false

  @HostListener('window:scroll', [])
  onWindowScroll() {
    this.isScrolled = window.pageYOffset > 50
  }

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen
  }
}
