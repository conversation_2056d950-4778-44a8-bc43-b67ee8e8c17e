import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { AccountService, ILoginApiResponse } from '../../Utils/_services/account.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './login.component.html',
  styleUrl: './login.component.css'
})
export class LoginComponent implements OnInit {
  loginModel = {
    usernameOrEmail: '',
    password: ''
  };

  isLoading = false;
  showPassword = false;
  errorMessage = '';
  rememberMe = false;

  constructor(
    private accountService: AccountService,
    private router: Router,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    const currentUser = this.accountService.currentUserSource.value;
    if (currentUser) {
      const redirectUrl = this.getRedirectUrlByRole(currentUser.role);
      this.router.navigate([redirectUrl]);
    }

    const savedCredentials = localStorage.getItem('rememberedCredentials');
    if (savedCredentials) {
      const credentials = JSON.parse(savedCredentials);
      this.loginModel.usernameOrEmail = credentials.usernameOrEmail;
      this.rememberMe = true;
    }
  }

  onSubmit(): void {
    if (this.isLoading) return;

    if (!this.loginModel.usernameOrEmail || !this.loginModel.password) {
      this.errorMessage = 'Please enter both username/email and password.';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    this.accountService.loginUser(this.loginModel.usernameOrEmail, this.loginModel.password).subscribe({
      next: (response: ILoginApiResponse) => {
        this.isLoading = false;
        console.log("Login Response:", response);
        console.log("Response data:", response.data);
        console.log("Role from data:", response.data?.role);
        console.log("Data keys:", response.data ? Object.keys(response.data) : 'No data');

        if (response.auth && response.auth.success) {
          if (this.rememberMe) {
            localStorage.setItem('rememberedCredentials', JSON.stringify({
              usernameOrEmail: this.loginModel.usernameOrEmail
            }));
          } else {
            localStorage.removeItem('rememberedCredentials');
          }

          this.toastr.success(response.msg || 'Login successful!');

          const redirectUrl = response.auth.redirectUrl || this.getRedirectUrlByRole(response.data?.role || undefined);
          this.router.navigate([redirectUrl]);
        } else {
          this.errorMessage = response.msg || 'Login failed. Please try again.';
          this.toastr.error(this.errorMessage);
        }
      },
      error: (err) => {
        this.isLoading = false;
        this.errorMessage = err.error?.message || 'Login failed. Please check your credentials and try again.';
        this.toastr.error(this.errorMessage);
        console.error('Login error:', err);
      }
    });
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  navigateToRegister(): void {
    this.router.navigate(['/home/<USER>']);
  }

  navigateToForgotPassword(): void {
    this.toastr.info('Forgot password feature coming soon!');
  }

  clearError(): void {
    this.errorMessage = '';
  }

  private getRedirectUrlByRole(role?: string): string {
    if (role && (role.toLowerCase() === 'admin' || role.toLowerCase() === 'administrator')) {
      return '/admin/dashboard';
    }
    return '/home';
  }
}
