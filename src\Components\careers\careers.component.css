:root {
  --gold-color: #ffd700;
  --purple-color: #8a2be2;
}

.careers-container {
  min-height: 100vh;
}
.file-drop-zone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease, background-color 0.3s ease;
  position: relative;
}

.file-drop-zone.drag-active {
  border-color: #007bff;
  background-color: #f0f8ff;
}

.file-drop-zone .drop-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.file-drop-zone .upload-icon {
  font-size: 2rem;
}

.file-drop-zone .file-input {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  top: 0;
  left: 0;
}

.file-help {
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 0.5rem;
}

/* Hero Section */
.careers-hero {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    var(--gold-color) 0%,
    rgba(255, 215, 0, 0.8) 25%,
    rgba(87, 87, 87, 0.9) 75%,
    rgba(0, 0, 0, 0.95) 100%
  );
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 215, 0, 0.1);
  animation: float 8s ease-in-out infinite;
}

.circle-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 20%;
  animation-delay: 3s;
}

.circle-3 {
  width: 80px;
  height: 80px;
  bottom: 30%;
  left: 70%;
  animation-delay: 6s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.hero-content {
  text-align: center;
  z-index: 2;
  max-width: 800px;
  padding: 80px 0;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.5);
  border-radius: 30px;
  padding: 12px 24px;
  margin-bottom: 1rem;
  color: white;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.badge-icon {
  font-size: 1.2rem;
}

.hero-title {
  font-size: 4rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  color: white;
  display: flex;
  justify-content: center;
  gap: 1rem;
}



.title-word:nth-child(1) {
  animation-delay: 0.2s;
}


@keyframes titleSlide {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-description {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 3rem;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #fff;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: rgba(255, 255, 255, 0.3);
}

/* Main Content */
.careers-content {
  padding: 4rem 0;
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Filters Section */
.filters-section {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.filters-title {
  font-size: 1.8rem;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.clear-filters-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  border: 2px solid var(--gold-color);
  color: var(--gold-color);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.clear-filters-btn:hover {
  background: var(--gold-color);
  color: #333;
  transform: translateY(-2px);
}

.filters-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 2rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.search-group {
  grid-column: span 1;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.label-icon {
  font-size: 1rem;
}

.search-input,
.filter-select {
  padding: 0.75rem 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.search-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--gold-color);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.search-input {
  width: 100%;
}

/* Jobs Section */
.jobs-section {
  margin-top: 2rem;
}

.jobs-header {
  margin-bottom: 2rem;
}

.jobs-count {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.jobs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2rem;
}

.job-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
}

.job-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.job-card.featured {
  border-color: var(--gold-color);
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.05) 0%, white 100%);
}

.job-card.expiring-soon {
  border-color: #ff6b6b;
}

.featured-badge,
.expiring-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.featured-badge {
  background: var(--gold-color);
  color: #333;
}

.expiring-badge {
  background: #ff6b6b;
  color: white;
}

.job-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.company-info {
  display: flex;
  gap: 1rem;
  flex: 1;
}

.company-logo {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  overflow: hidden;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.company-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.company-details {
  flex: 1;
}

.job-title {
  font-size: 1.4rem;
  font-weight: bold;
  color: #333;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.company-name {
  color: #666;
  margin: 0;
  font-size: 0.95rem;
}

.job-type-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(138, 43, 226, 0.1);
  color: var(--purple-color);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  white-space: nowrap;
}

.type-icon {
  font-size: 1rem;
}

.job-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-size: 0.9rem;
}

.detail-icon {
  font-size: 1rem;
}

.job-description {
  margin-bottom: 1.5rem;
}

.job-description p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.job-requirements {
  margin-bottom: 2rem;
}

.requirements-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.75rem 0;
}

.requirements-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.requirement-tag {
  background: rgba(255, 215, 0, 0.1);
  color: #333;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.job-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1.5rem;
  border-top: 1px solid #f0f0f0;
}

.job-dates {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.posted-date,
.end-date {
  color: #999;
  font-size: 0.8rem;
}

.apply-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(45deg, var(--purple-color), var(--gold-color));
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.apply-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(138, 43, 226, 0.4);
}

.btn-icon {
  font-size: 1rem;
}

/* No Jobs Found */
.no-jobs {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.no-jobs-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.no-jobs h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.no-jobs p {
  margin-bottom: 2rem;
}

/* Application Dialog */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.application-dialog {
  background: white;
  border-radius: 20px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.dialog-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.dialog-icon {
  font-size: 2rem;
  background: linear-gradient(45deg, var(--gold-color), var(--purple-color));
  padding: 0.75rem;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-title h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.dialog-title p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f0f0f0;
  color: #333;
}

.application-form {
  padding: 2rem;
}

.form-section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--gold-color);
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: span 2;
}

.form-group label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-input,
.form-textarea {
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--gold-color);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.file-upload-section {
  display: flex;
  gap: 1rem;
}

.file-upload-group {
  flex: 1;
}

.file-upload-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px dashed #e0e0e0;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.file-upload-label:hover {
  border-color: var(--gold-color);
  background: rgba(255, 215, 0, 0.05);
}

.file-input {
  display: none;
}

.upload-icon {
  font-size: 1.5rem;
}

.file-help {
  color: #666;
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid #f0f0f0;
}

.cancel-btn {
  background: transparent;
  border: 2px solid #ddd;
  color: #666;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  border-color: #999;
  color: #333;
}

.submit-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(45deg, var(--purple-color), #9932cc);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(138, 43, 226, 0.4);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .filters-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  .search-group {
    grid-column: span 2;
  }

  .jobs-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 3rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1.5rem;
  }

  .stat-divider {
    width: 40px;
    height: 1px;
  }

  .filters-grid {
    grid-template-columns: 1fr;
  }

  .search-group {
    grid-column: span 1;
  }

  .filters-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .job-header {
    flex-direction: column;
    gap: 1rem;
  }

  .job-footer {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-group.full-width {
    grid-column: span 1;
  }

  .dialog-footer {
    flex-direction: column;
  }

  .application-dialog {
    width: 95%;
    margin: 1rem;
  }
}

@media (max-width: 480px) {
  .content-container {
    padding: 0 1rem;
  }

  .filters-section,
  .job-card {
    padding: 1.5rem;
  }

  .hero-content {
    padding: 1rem;
  }

  .application-form {
    padding: 1rem;
  }

  .dialog-header {
    padding: 1.5rem 1rem 1rem;
  }
}
