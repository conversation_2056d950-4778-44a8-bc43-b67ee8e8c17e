import { Component } from '@angular/core';
import { PageTitleService } from '../../Utils/_services/page-title.service';
import { AppliedJobService } from '../../services/applied-job.service';
import { CommonModule, NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'app-appliedjobs',
  standalone: true,
  imports: [NgIf,NgFor,CommonModule],
  templateUrl: './appliedjobs.component.html',
  styleUrl: './appliedjobs.component.css'
})
export class AppliedjobsComponent {

  appliedJobs: any[] = [];
  totalCount: number = 0;
  pageNumber: number = 1;
  pageSize: number = 10;
  errorMessage: string = '';

  constructor(private pageTitle : PageTitleService,
     private appliedJob : AppliedJobService
    ){}
    
  ngOnInit(): void {
  this.pageTitle.setTitle('Applied jobs');
  this.loadAppliedJobs()
 }

 loadAppliedJobs(): void {
    this.errorMessage = '';

    this.appliedJob.getAllAppliedJobs(this.pageNumber, this.pageSize).subscribe({
      next: (response) => {
        console.log("REs",response.items)
        this.appliedJobs = response.items;
        console.log("Job",this.appliedJob)
        this.totalCount = response.totalCount;
      },
      error: (err) => {
        console.error('Failed to fetch applied jobs', err);
        this.errorMessage = 'Failed to load applied jobs. Please try again later.';
      }
    });
  }

   nextPage() {
    if ((this.pageNumber * this.pageSize) < this.totalCount) {
      this.pageNumber++;
      this.loadAppliedJobs();
    }
  }

  prevPage() {
    if (this.pageNumber > 1) {
      this.pageNumber--;
      this.loadAppliedJobs();
    }
  }

}
