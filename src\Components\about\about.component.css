:root {
  --gold-color: #ffd700;
  --purple-color: #8a2be2;
}

.about-container {
  min-height: 100vh;
}

/* Hero Section */
.about-hero {
  position: relative;
  min-height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    var(--gold-color) 0%,
    rgba(255, 215, 0, 0.8) 25%,
    rgba(87, 87, 87, 0.9) 75%,
    rgba(0, 0, 0, 0.95) 100%
  );
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 215, 0, 0.1);
  animation: float 8s ease-in-out infinite;
}

.circle-1 {
  width: 100px;
  height: 100px;
  top: 15%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

.circle-4 {
  width: 120px;
  height: 120px;
  top: 30%;
  right: 30%;
  animation-delay: 6s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
  }
}

.hero-content {
  text-align: center;
  z-index: 2;
  max-width: 900px;
  padding: 80px 0;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.5);
  border-radius: 30px;
  padding: 12px 24px;
  margin-bottom: 1rem;
  color: white;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.badge-icon {
  font-size: 1.2rem;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.hero-title {
  font-size: 4.5rem;
  font-weight: bold;
  margin-bottom: 2rem;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.title-word {
  display: inline-block;
  animation: titleSlide 1s ease-out forwards;
  opacity: 0;
  transform: translateY(50px);
}


@keyframes titleSlide {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-description {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 3rem;
  line-height: 1.6;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #fff;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

/* Main Content */
.about-content {
  padding: 6rem 0;
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Mission & Vision Cards */
.mission-vision-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 3rem;
  margin-bottom: 6rem;
}

.about-card {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.about-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.card-icon {
  font-size: 2.5rem;
  padding: 1rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
}

.card-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.card-content {
  margin-bottom: 1.5rem;
}

.card-description {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #666;
  margin: 0;
}

.card-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  overflow: hidden;
}

.decoration-line {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, var(--gold-color), var(--purple-color));
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Values Section */
.values-section {
  margin-bottom: 6rem;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 3rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 1rem;
}

.section-underline {
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, var(--gold-color), var(--purple-color));
  margin: 0 auto 1.5rem;
  border-radius: 2px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin: 0;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.value-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  position: relative;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
}

.value-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.value-icon-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.value-icon {
  font-size: 3rem;
  padding: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin: 0 auto;
}

.icon-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: iconGlow 3s ease-in-out infinite alternate;
}

@keyframes iconGlow {
  0% {
    opacity: 0.5;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1.1);
  }
}

.value-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.value-description {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.value-number{
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 30px;
    height: 30px;
    /* background: var(--gold-color); */
    color: #333;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 40px;
    opacity: .1;
}

/* Story Section */
.story-section {
  background: white;
  border-radius: 20px;
  padding: 4rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 4rem;
}

.story-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.story-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 1rem;
}

.story-underline {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--gold-color), var(--purple-color));
  margin-bottom: 2rem;
  border-radius: 2px;
}

.story-description {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #666;
  margin-bottom: 2rem;
}

.story-highlights {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #333;
  font-weight: 500;
}

.highlight-icon {
  font-size: 1.2rem;
  background: linear-gradient(45deg, var(--gold-color), var(--purple-color));
  padding: 0.5rem;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.story-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.visual-container {
  position: relative;
  width: 300px;
  height: 300px;
}

.visual-circle {
  position: absolute;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-circle {
  width: 150px;
  height: 150px;
  background: linear-gradient(45deg, var(--gold-color), var(--purple-color));
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.circle-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
}

.circle-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.circle-text {
  font-weight: bold;
  font-size: 1.1rem;
}

.orbit-circle {
  width: 60px;
  height: 60px;
  background: white;
  border: 3px solid var(--gold-color);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.orbit-1 {
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  animation: orbit1 8s linear infinite;
}

.orbit-2 {
  bottom: 20px;
  right: 20px;
  animation: orbit2 10s linear infinite;
}

.orbit-3 {
  bottom: 20px;
  left: 20px;
  animation: orbit3 12s linear infinite;
}

@keyframes orbit1 {
  from {
    transform: translateX(-50%) rotate(0deg) translateX(100px) rotate(0deg);
  }
  to {
    transform: translateX(-50%) rotate(360deg) translateX(100px) rotate(-360deg);
  }
}

@keyframes orbit2 {
  from {
    transform: rotate(0deg) translateX(100px) rotate(0deg);
  }
  to {
    transform: rotate(360deg) translateX(100px) rotate(-360deg);
  }
}

@keyframes orbit3 {
  from {
    transform: rotate(0deg) translateX(100px) rotate(0deg);
  }
  to {
    transform: rotate(-360deg) translateX(100px) rotate(360deg);
  }
}

.orbit-icon {
  font-size: 1.5rem;
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(87, 87, 87, 0.9) 50%, var(--gold-color) 100%);
  padding: 4rem 0;
  text-align: center;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.cta-title {
  font-size: 3rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.cta-description {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 3rem;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 2rem;
}

.cta-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.cta-button.primary {
  background: linear-gradient(45deg, var(--purple-color), var(--gold-color));
  color: white;
}

.cta-button.primary:hover {
  transform: translateY(-3px);
}

.cta-button.secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.cta-button.secondary:hover {
  background: white;
  color: #333;
  transform: translateY(-3px);
}

.button-icon {
  font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 3rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1.5rem;
  }

  .mission-vision-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .values-grid {
    grid-template-columns: 1fr;
  }

  .story-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .story-section {
    padding: 2rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-button {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .visual-container {
    width: 250px;
    height: 250px;
  }

  .main-circle {
    width: 120px;
    height: 120px;
  }

  .orbit-circle {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 480px) {
  .about-container {
    padding-top: 70px;
  }

  .hero-content {
    padding: 1rem;
  }

  .content-container {
    padding: 0 1rem;
  }

  .about-card,
  .value-card {
    padding: 1.5rem;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .cta-title {
    font-size: 2.5rem;
  }
}
