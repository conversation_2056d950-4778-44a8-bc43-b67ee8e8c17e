import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { NavigationEnd, Router, RouterModule } from '@angular/router';
import { filter } from 'rxjs';
interface SidebarItem {
  label: string;
  icon?: string;
  route: string;
}

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule,RouterModule],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.css'
})
export class SidebarComponent {
 @Input() collapsed: boolean = false;
  currentRoute = '';

  sidebarItems: SidebarItem[] = [
    { label: 'Dashboard', icon: 'bi-speedometer2', route: '/admin/dashboard' },
    { label: 'App Management', icon: 'bi-grid', route: '/admin/app-management' },
    { label: 'Post Jobs', icon: 'bi-briefcase', route: '/admin/job-post' },
    { label: 'Applied Jobs', icon: 'bi-bug', route: '/admin/appliedjobs' },
    { label: 'Manage Users', icon: 'bi-people', route: '/admin/users' },
    { label: 'Newsletter Blog', icon: 'bi-journal', route: '/admin/newsletters' },
    { label: 'Bug Reporting', icon: 'bi-bug', route: '/admin/bug-reporting' },
  ];

  constructor(private router: Router) {
    // Listen to route changes to update current route
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: NavigationEnd) => {
      this.currentRoute = event.url;
    });

    // Set initial current route
    this.currentRoute = this.router.url;
  }

  navigate(item: SidebarItem) {
    console.log('Navigating to:', item.route);
    this.router.navigate([item.route]);
  }

  isActive(route: string): boolean {
    return this.currentRoute === route;
  }
}
