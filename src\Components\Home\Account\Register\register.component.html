<div class="register-page">
  <!-- Hero Section -->
  <section class="register-hero">
    <div class="hero-background">
      <div class="floating-elements">
        <div class="floating-circle circle-1"></div>
        <div class="floating-circle circle-2"></div>
        <div class="floating-circle circle-3"></div>
      </div>
    </div>
    
    <div class="hero-content">
      <div class="hero-badge">
        <span class="badge-icon">🚀</span>
        <span>Join Our Platform</span>
      </div>
      
      <h1 class="hero-title">
        <span class="title-word">Create</span>
        <span class="title-word">Your</span>
        <span class="title-word highlight">Account</span>
      </h1>
      
      <p class="hero-description">
        Start your journey with us today and unlock amazing features
      </p>
    </div>
  </section>

  <!-- Registration Form -->
  <div class="register-content">
    <div class="content-container">
      <div class="form-wrapper">
        <div class="form-container">
          <div class="form-header">
            <div class="form-icon">
              <span>👤</span>
            </div>
            <h2 class="form-title">Sign Up</h2>
            <p class="form-subtitle">Fill in your details to create an account</p>
          </div>

          <form #registerForm="ngForm" (ngSubmit)="onSubmitForm()" autocomplete="off" class="registration-form">
            
            <!-- Name Fields Row -->
            <div class="form-row">
              <div class="form-group">
            
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  class="form-input"
                  [(ngModel)]="model.firstName"
                  required
                  #firstName="ngModel"
                  placeholder="Enter first name"
                />
                <div *ngIf="firstName.invalid && (firstName.dirty || firstName.touched)" class="error-message">
                  <span class="error-icon">⚠️</span>
                  <small *ngIf="firstName.errors?.['required']">First name is required.</small>
                </div>
              </div>

              <div class="form-group">
                
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  class="form-input"
                  [(ngModel)]="model.lastName"
                  required
                  #lastName="ngModel"
                  placeholder="Enter last name"
                />
                <div *ngIf="lastName.invalid && (lastName.dirty || lastName.touched)" class="error-message">
                  <span class="error-icon">⚠️</span>
                  <small *ngIf="lastName.errors?.['required']">Last name is required.</small>
                </div>
              </div>
            </div>

            <!-- Email Field -->
            <div class="form-group">
           
              <input
                type="email"
                id="email"
                name="email"
                class="form-input"
                [(ngModel)]="model.email"
                required
                email
                #email="ngModel"
                placeholder="Enter your email address"
              />
              <div *ngIf="email.invalid && (email.dirty || email.touched)" class="error-message">
                <span class="error-icon">⚠️</span>
                <small *ngIf="email.errors?.['required']">Email is required.</small>
                <small *ngIf="email.errors?.['email']">Please enter a valid email address.</small>
              </div>
            </div>

            <!-- Username Field -->
            <div class="form-group">
             
              <input
                type="text"
                id="username"
                name="username"
                class="form-input"
                [(ngModel)]="model.username"
                required
                #username="ngModel"
                placeholder="Choose a unique username"
              />
              <div *ngIf="username.invalid && (username.dirty || username.touched)" class="error-message">
                <span class="error-icon">⚠️</span>
                <small *ngIf="username.errors?.['required']">Username is required.</small>
              </div>
            </div>

            <!-- Date of Birth Field -->
            <div class="form-group">
             
              <input
                type="date"
                id="dob"
                name="dob"
                class="form-input"
                [(ngModel)]="model.dob"
                required
                #dob="ngModel"
              />
              <div *ngIf="dob.invalid && (dob.dirty || dob.touched)" class="error-message">
                <span class="error-icon">⚠️</span>
                <small *ngIf="dob.errors?.['required']">Date of birth is required.</small>
              </div>
            </div>

            <!-- Password Field -->
            <div class="form-group">
          
              <input
                type="password"
                id="password"
                name="password"
                class="form-input"
                [(ngModel)]="model.password"
                required
                minlength="6"
                #password="ngModel"
                placeholder="Create a secure password"
              />
              <div class="password-strength">
                <div class="strength-bar">
                  <div class="strength-fill"></div>
                </div>
                <span class="strength-text">Password strength: Medium</span>
              </div>
              <div *ngIf="password.invalid && (password.dirty || password.touched)" class="error-message">
                <span class="error-icon">⚠️</span>
                <small *ngIf="password.errors?.['required']">Password is required.</small>
                <small *ngIf="password.errors?.['minlength']">Password must be at least 6 characters.</small>
              </div>
            </div>

            <!-- Newsletter Checkbox -->
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input
                  class="checkbox-input"
                  type="checkbox"
                  id="newsletter"
                  name="newsletter"
                  [(ngModel)]="model.newsletter"
                  #newsletter="ngModel"
                />
                <span class="checkbox-custom"></span>
                <span class="checkbox-text">
                  <span class="checkbox-icon">📧</span>
                  Subscribe to our newsletter for updates and exclusive offers
                </span>
              </label>
                <!-- Terms and Conditions -->
            <div class="terms-section mt-2">
              <p class="terms-text">
                By creating an account, you agree to our 
                <a href="#" class="terms-link">Terms of Service</a> 
                and 
                <a href="#" class="terms-link">Privacy Policy</a>
              </p>
            </div>
            </div>

          

            <!-- Form Actions -->
            <div class="form-actions">
              <button type="submit" class="submit-btn" [disabled]="registerForm.invalid">
                <span class="btn-content" *ngIf="!registerForm.invalid">
                  <span class="btn-icon">🚀</span>
                  <span>Create Account</span>
                </span>
                <span class="btn-content" *ngIf="registerForm.invalid">
                  <span class="btn-icon">⚠️</span>
                  <span>Complete Form</span>
                </span>
              </button>
              
       
            </div>

            <!-- Login Link -->
            <!-- <div class="login-link-section">
              <p class="login-text">
                Already have an account? 
                <a href="#" class="login-link">Sign in here</a>
              </p>
            </div> -->
          </form>
        </div>

        <!-- Side Panel -->
        <div class="side-panel">
          <div class="panel-content">
            <div class="panel-icon">
              <span>✨</span>
            </div>
            <h3 class="panel-title">Why Join Us?</h3>
            
            <div class="benefits-list">
              <div class="benefit-item">
                <span class="benefit-icon">🎯</span>
                <div class="benefit-content">
                  <h4>Personalized Experience</h4>
                  <p>Get tailored content and recommendations</p>
                </div>
              </div>
              
              <div class="benefit-item">
                <span class="benefit-icon">🔒</span>
                <div class="benefit-content">
                  <h4>Secure & Private</h4>
                  <p>Your data is protected with enterprise-grade security</p>
                </div>
              </div>
              
              <div class="benefit-item">
                <span class="benefit-icon">🚀</span>
                <div class="benefit-content">
                  <h4>Exclusive Features</h4>
                  <p>Access premium tools and early feature releases</p>
                </div>
              </div>
              
              <div class="benefit-item">
                <span class="benefit-icon">🤝</span>
                <div class="benefit-content">
                  <h4>24/7 Support</h4>
                  <p>Get help whenever you need it from our team</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
