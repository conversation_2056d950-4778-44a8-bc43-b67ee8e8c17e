import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AccountService } from '../../Utils/_services/account.service';
import { PageTitleService } from '../../Utils/_services/page-title.service';

@Component({
  selector: 'app-users',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './users.component.html',
  styleUrl: './users.component.css'
})
export class UsersComponent implements OnInit {
  users: any[] = [];
  totalCount: number = 0;
  pageNumber: number = 1;
  pageSize: number = 10;
  totalPages: number = 1;
  isLoading: boolean = false;
  errorMessage: string = '';
  updatingUsers: Set<number> = new Set();

  constructor(
    private accountService: AccountService,
    private pageTitleService: PageTitleService
  ) {}

  ngOnInit(): void {
    this.pageTitleService.setTitle('Manage Users');
    this.loadUsers();
  }

  loadUsers(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.accountService.GetAllUser(this.pageNumber, this.pageSize).subscribe({
      next: (response) => {
        this.users = response.items || [];
        this.totalCount = response.totalCount || 0;
        this.totalPages = Math.ceil(this.totalCount / this.pageSize);
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Failed to fetch users', err);
        this.errorMessage = 'Failed to load users. Please try again later.';
        this.isLoading = false;
      }
    });
  }

  nextPage(): void {
    if (this.pageNumber < this.totalPages) {
      this.pageNumber++;
      this.loadUsers();
    }
  }

  prevPage(): void {
    if (this.pageNumber > 1) {
      this.pageNumber--;
      this.loadUsers();
    }
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.pageNumber) {
      this.pageNumber = page;
      this.loadUsers();
    }
  }

  onPageSizeChange(): void {
    this.pageNumber = 1;
    this.loadUsers();
  }

  toggleNewsletter(user: any): void {
    if (this.updatingUsers.has(user.id)) return;

    const originalValue = user.newsletter;
    user.newsletter = !user.newsletter;
    this.updatingUsers.add(user.id);
    this.errorMessage = '';

    const updatedUser = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      userName: user.userName,
      dob: user.dob,
      newsletter: user.newsletter,
      isActive: user.isActive,
      role: user.role
    };

    this.accountService.UpdateUser(updatedUser).subscribe({
      next: () => {
        console.log(`Newsletter updated successfully for user ${user.userName}: ${user.newsletter}`);
        this.updatingUsers.delete(user.id);
      },
      error: (err) => {
        user.newsletter = originalValue;
        console.error('Failed to update newsletter status:', err);
        this.errorMessage = 'Failed to update newsletter status. Please try again.';
        this.updatingUsers.delete(user.id);
      }
    });
  }

  toggleUserStatus(user: any): void {
    if (this.updatingUsers.has(user.id)) return;

    const originalValue = user.isActive;
    user.isActive = !user.isActive;
    this.updatingUsers.add(user.id);
    this.errorMessage = '';

    const updatedUser = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      userName: user.userName,
      dob: user.dob,
      newsletter: user.newsletter,
      isActive: user.isActive,
      role: user.role
    };

    this.accountService.UpdateUser(updatedUser).subscribe({
      next: () => {
        console.log(`User status updated successfully for ${user.userName}: ${user.isActive ? 'Active' : 'Inactive'}`);
        this.updatingUsers.delete(user.id);
      },
      error: (err) => {
        user.isActive = originalValue;
        console.error('Failed to update user status:', err);
        this.errorMessage = 'Failed to update user status. Please try again.';
        this.updatingUsers.delete(user.id);
      }
    });
  }

  isUserUpdating(userId: number): boolean {
    return this.updatingUsers.has(userId);
  }

  getPaginationArray(): number[] {
    const maxVisible = 5;
    const start = Math.max(1, this.pageNumber - Math.floor(maxVisible / 2));
    const end = Math.min(this.totalPages, start + maxVisible - 1);
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  getStartIndex(): number {
    return (this.pageNumber - 1) * this.pageSize + 1;
  }

  getEndIndex(): number {
    return Math.min(this.pageNumber * this.pageSize, this.totalCount);
  }
}
