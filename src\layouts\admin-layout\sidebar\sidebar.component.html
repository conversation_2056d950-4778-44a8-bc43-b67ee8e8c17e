<div [ngClass]="{ 'sidebar': true, 'collapsed': collapsed }">
  <div class="sidebar-heading text-center py-3">
    <h4>Admin</h4>
  </div>
  <div class="list-group list-group-flush">
    <a *ngFor="let item of sidebarItems"
       (click)="navigate(item)"
       [ngClass]="{
         'list-group-item': true,
         'list-group-item-action': true,
         'bg-light': !isActive(item.route),
         'text-dark': !isActive(item.route),
         'border-0': true,
         'd-flex': true,
         'align-items-center': true,
         'gap-2': true,
         'active': isActive(item.route),
         'selected': isActive(item.route)
       }"
       class="sidebar-item">
      <i *ngIf="item.icon" class="bi" [ngClass]="item.icon"></i>
      <span [ngClass]="{ 'collapsed': collapsed }">{{ item.label }}</span>
    </a>
  </div>
</div>