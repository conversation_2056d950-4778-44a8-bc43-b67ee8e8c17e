<div class="home-container">
  <!-- Hero Section -->
  <section class="hero-section">
    <div class="hero-background">
      <div class="floating-elements">
        <div class="floating-circle circle-1"></div>
        <div class="floating-circle circle-2"></div>
        <div class="floating-circle circle-3"></div>
        <div class="floating-circle circle-4"></div>
      </div>
    </div>

    <div class="hero-content">
      <div class="hero-badge">
        <span class="badge-icon">✨</span>
        <span>A Software Company</span>
      </div>

      <h1 class="hero-title damion-regular">
        <span class="title-word">Innovation Meets Excellence</span>
      </h1>

      <p class="hero-subtitle">
        Transforming ideas into powerful digital solutions that drive your business forward
      </p>

      <div class="hero-stats">
        <div class="stat-item">
          <div class="stat-number">500+</div>
          <div class="stat-label">Projects Delivered</div>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-item">
          <div class="stat-number">50+</div>
          <div class="stat-label">Happy Clients</div>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-item">
          <div class="stat-number">24/7</div>
          <div class="stat-label">Support</div>
        </div>
      </div>
    </div>
  </section>
  <section class="apps-carousel-section py-5 bg-light">
    <div class="container py-5">
      <h2 class="text-center mb-4">Our Products</h2>

      <div id="productCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="6000">
        <!-- Carousel Inner -->
        <div class="carousel-inner">
          <div class="carousel-item" *ngFor="let appGroup of groupedApps; let i = index" [class.active]="i === 0">

            <div class="row">
              <div *ngFor="let app of appGroup" class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 shadow-sm rounded-4">
                  <img [src]="app.logoUrl || 'assets/placeholder.png'" class="card-img-top p-3 mx-auto d-block"
                    [alt]="app.title || 'App Logo'" style="height: 150px; width: auto; object-fit: contain;">

                  <div class="card-body text-center">
                    <h5 class="card-title fw-bold">{{ app.title }}</h5>
                    <p class="card-text text-muted">
                      {{ app.description || 'No description available.' }}
                    </p>
                  </div>
                  <div class="row g-2 justify-content-center mt-3">
                    <div class="col-auto">
                      <button class="btn btn-primary px-3" (click)="openWebsite(app.websiteUrl)">
                        Visit Website →
                      </button>
                    </div>
                    <div class="col-auto">
                      <button class="btn btn-outline-primary px-3" (click)="openWebsite(app.storeUrl)">
                        Visit Store →
                      </button>
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </section>

  <!-- Enhanced Maxgeneye Section -->
  <section class="maxgeneye-section">
    <div class="section-header">
      <h2 class="section-title">Featured Product</h2>
      <div class="section-underline"></div>
    </div>

    <div class="body-content-container">
      <!-- Animated background elements -->
      <div class="floating-elements">
        <div class="floating-circle circle-1"></div>
        <div class="floating-circle circle-2"></div>
        <div class="floating-circle circle-3"></div>
      </div>

      <div class="bc-left">
        <div class="bc-left-top p-0 m-0">
          <div class="title-container">
            <h4 class="body-content-title">
              <span class="title-letter">M</span>
              <span class="title-letter">a</span>
              <span class="title-letter">x</span>
              <span class="title-letter">g</span>
              <span class="title-letter">e</span>
              <span class="title-letter">n</span>
              <span class="title-letter">e</span>
              <span class="title-letter">y</span>
              <span class="title-letter">e</span>
            </h4>
            <div class="title-underline"></div>
          </div>
        </div>

        <div class="bc-left-botton p-0 m-0">
          <div class="h-50 p-2 d-flex align-items-center justify-content-center description-container">
            <div class="description-content">
              <div class="feature-badge">
                <span class="badge-icon">⚡</span>
                <span>Revolutionary Platform</span>
              </div>
              <p class="main-description">
                A powerful website that allows users to never have to apply to jobs again.
              </p>
              <div class="feature-list">
                <div class="feature-item">
                  <span class="feature-dot"></span>
                  <span>AI-Powered Job Matching</span>
                </div>
                <div class="feature-item">
                  <span class="feature-dot"></span>
                  <span>Automated Applications</span>
                </div>
                <div class="feature-item">
                  <span class="feature-dot"></span>
                  <span>Smart Profile Optimization</span>
                </div>
              </div>
            </div>
          </div>

          <div class="h-50 d-flex align-items-center justify-content-center button-container">
            <div class="cta-section">
              <button type="button" class="btn btn-button enhanced-button" (click)="visitMaxgeneye()">
                <span class="button-content">
                  <span class="button-icon">🚀</span>
                  <span class="button-text">Visit Website</span>
                  <span class="button-arrow">→</span>
                </span>
                <div class="button-glow"></div>
              </button>
              <p class="cta-subtitle">Transform your job search today</p>
            </div>
          </div>
        </div>
      </div>

      <div class="bc-right">
        <div class="logo-container">
          <div class="logo-wrapper">
            <div class="logo-glow-effect"></div>
            <img
              src="https://insizon-email-templates-bucket.s3.us-east-2.amazonaws.com/maxgeneye/maxgeneye-logo-v1-png.png"
              alt="Maxgeneye logo" class="logo-image" />
            <div class="logo-particles">
              <div class="particle particle-1"></div>
              <div class="particle particle-2"></div>
              <div class="particle particle-3"></div>
              <div class="particle particle-4"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Decorative elements -->
      <div class="decorative-lines">
        <div class="line line-1"></div>
        <div class="line line-2"></div>
        <div class="line line-3"></div>
      </div>
    </div>
  </section>
</div>