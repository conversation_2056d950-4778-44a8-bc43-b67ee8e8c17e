import { Component, EventEmitter, Output } from '@angular/core';
import { PageTitleService } from '../../../Utils/_services/page-title.service';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css'
})
export class HeaderComponent {
  @Output() toggleSidebar = new EventEmitter<void>();

  onToggleClick() {
    this.toggleSidebar.emit();
  }

   pageTitle = 'Admin Panel';

  constructor(private pageTitleService: PageTitleService) {}

  ngOnInit(): void {
    this.pageTitleService.title$.subscribe(title => {
      this.pageTitle = title;
    });
  }
}
