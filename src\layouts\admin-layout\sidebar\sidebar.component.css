.sidebar {
  min-height: 100vh;
  background-color: #f8f9fa;
  border-right: 1px solid #dee2e6;
  transition: all 0.3s ease;
  width: 250px;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-heading {
  background-color: #343a40;
  color: white;
  border-bottom: 1px solid #dee2e6;
}

.sidebar.collapsed .sidebar-heading h4 {
  display: none;
}

.sidebar-item {
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 12px 20px;
  text-decoration: none;
  color: #495057;
}

.sidebar-item:hover {
  background-color: #e9ecef !important;
  color: #495057 !important;
  text-decoration: none;
}

/* Active/Selected state styles */
.sidebar-item.active,
.sidebar-item.selected {
  background-color: #007bff !important;
  color: white !important;
  border-left: 4px solid #0056b3;
  font-weight: 500;
}

.sidebar-item.active:hover,
.sidebar-item.selected:hover {
  background-color: #0056b3 !important;
  color: white !important;
}

.sidebar-item.active i,
.sidebar-item.selected i {
  color: white;
}

/* Icon styles */
.sidebar-item i {
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
  transition: color 0.2s ease;
}

/* Collapsed sidebar styles */
.sidebar.collapsed .sidebar-item span {
  display: none;
}

.sidebar.collapsed .sidebar-item {
  justify-content: center;
  padding: 12px 10px;
}

.sidebar.collapsed .sidebar-item i {
  font-size: 1.3rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    min-height: auto;
  }
  
  .sidebar.collapsed {
    width: 100%;
  }
  
  .sidebar.collapsed .sidebar-item span {
    display: inline;
  }
}

/* Additional visual enhancements */
.sidebar-item {
  position: relative;
  overflow: hidden;
}

.sidebar-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: left 0.5s;
}

.sidebar-item:hover::before {
  left: 100%;
}

/* Focus states for accessibility */
.sidebar-item:focus {
  outline: 2px solid #007bff;
  outline-offset: -2px;
}

.sidebar-item.active:focus,
.sidebar-item.selected:focus {
  outline-color: white;
}