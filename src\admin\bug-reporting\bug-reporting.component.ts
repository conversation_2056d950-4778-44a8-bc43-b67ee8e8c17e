import { CommonModule, Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { Component } from '@angular/core';
import { PageTitleService } from '../../Utils/_services/page-title.service';
import { Bug, BugReportingEntityDto, PagedResult } from '../../models/bug-report.model';
import { FormsModule, NgModel } from '@angular/forms';
import { BugReportingService } from '../../services/bug-reporting.service';

@Component({
  selector: 'app-bug-reporting',
  standalone: true,
  imports: [CommonModule,FormsModule],
  templateUrl: './bug-reporting.component.html',
  styleUrl: './bug-reporting.component.css'
})
export class BugReportingComponent {
  viewMode: 'card' | 'table' = 'card';
  showModal = false;
  selectedBug: BugReportingEntityDto | null = null;

  bugs: any[] = [];
  pageNumber = 1;
  pageSize = 10;
  totalItems = 0;
  totalPages = 0;


  constructor(private pageTitle : PageTitleService,
     private bugService: BugReportingService
  ){}

  ngOnInit(): void {
  this.pageTitle.setTitle('Bug Reporting');
  this.loadBugs();
 }

  loadBugs() {
    this.bugService.getAll(this.pageNumber, this.pageSize).subscribe({
      next: (result: PagedResult<BugReportingEntityDto>) => {
        this.bugs = result.items;
        this.totalItems = result.totalCount;
        this.totalPages = Math.ceil(this.totalItems / this.pageSize);
      },
      error: (err) => console.error('Failed to load bugs:', err)
    });
  }

   updateBug() {
    if (this.selectedBug && this.selectedBug.id) {
      this.bugService.update(this.selectedBug.id, this.selectedBug).subscribe({
        next: () => {
          // Refresh bug list after update
          this.loadBugs();
          this.closeModal();
        },
        error: (err) => console.error('Failed to update bug:', err)
      });
    }
  }

  toggleView() {
    this.viewMode = this.viewMode === 'card' ? 'table' : 'card';
  }

  viewBug(bug: Bug) {
    this.selectedBug = { ...bug, id: String(bug.id) };
    this.showModal = true;
  }

  closeModal() {
    this.showModal = false;
    this.selectedBug = null;
  }

  // updateBug() {
  //   if (this.selectedBug) {
  //     const index = this.bugs.findIndex(b => b.id === this.selectedBug!.id);
  //     if (index !== -1) {
  //       this.bugs[index] = { ...this.selectedBug };
  //     }
  //     this.closeModal();
  //   }
  // }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Open': return 'status-open';
      case 'In Progress': return 'status-progress';
      case 'Closed': return 'status-closed';
      default: return '';
    }
  }

   changePage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.pageNumber = page;
      this.loadBugs();
    }
  }

  getPriorityClass(priority: string): string {
    switch (priority) {
      case 'High': return 'priority-high';
      case 'Medium': return 'priority-medium';
      case 'Low': return 'priority-low';
      default: return '';
    }
  }
  

}
