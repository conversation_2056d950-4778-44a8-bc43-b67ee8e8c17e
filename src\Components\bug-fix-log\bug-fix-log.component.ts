import { CommonModule, NgFor } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { AppManagementService } from '../../services/app-management.service';
import { BugReportingService } from '../../services/bug-reporting.service';
import { BugReportingEntityDto } from '../../models/bug-report.model';

@Component({
  selector: 'app-bug-fix-log',
  standalone: true,
  imports: [NgFor, CommonModule, CardModule, DialogModule, 
    FormsModule, DropdownModule, ButtonModule],
  templateUrl: './bug-fix-log.component.html',
  styleUrl: './bug-fix-log.component.css'
})
export class BugFixLogComponent {

  showCreateDialog = false;
  showRequestDialog = false;

  bugs: any[] = [];

  apps: any[]= [];

  selectedApp: any = null;
  newBugTitle: string = '';
  newBugDescription: string = '';

  selectedBug: any = null;
  userComment: string = '';

    constructor(private appService: AppManagementService,
      private bugService: BugReportingService
    ) {}
  

  ngOnInit(): void {
    this.loadApps();
    this.loadBugs();
  }

  loadApps(): void {
  this.appService.getAllApps().subscribe({
    next: (response) => {
      this.apps = response;
      console.log("apps",this.apps)
    },
    error: (err) => {
      console.error('Failed to load all apps:', err);
      alert('Error loading apps. Please try again.');
    }
  });
 }

  loadBugs(): void {
    this.bugService.getAll(1, 50).subscribe({
      next: (response) => {
        this.bugs = response.items;
        console.log("Bugs loaded:", this.bugs);
      },
      error: (err) => {
        console.error('Failed to load bugs:', err);
        alert('Error loading bugs. Please try again.');
      }
    });
  }

  openCreateDialog() {
    this.showCreateDialog = true;
  }

  openRequestDialog() {
    this.showRequestDialog = true;
  }

  // submitNewBug() {
  //   if (!this.selectedApp || !this.newBugTitle || !this.newBugDescription) return;

  //   const newBug = {
  //     id: this.bugs.length + 1,
  //     app: this.selectedApp.name,
  //     title: this.newBugTitle,
  //     description: this.newBugDescription,
  //     status: 'open',
  //     createdAt: new Date()
  //   };

  //   this.bugs.push(newBug);

  //   // Clear fields
  //   this.selectedApp = null;
  //   this.newBugTitle = '';
  //   this.newBugDescription = '';
  //   this.showCreateDialog = false;
  // }

  submitNewBug() {
    if (!this.selectedApp || !this.newBugTitle || !this.newBugDescription) return;
    console.log("App id",this.selectedApp)
    const newBug: BugReportingEntityDto = {
      appId: this.selectedApp,
      title: this.newBugTitle,
      description: this.newBugDescription,
    };

    console.log("Tesing Data",newBug)

    this.bugService.create(newBug).subscribe({
      next: (createdBug) => {
        this.bugs.unshift(createdBug); // Add the new bug to the top of the list
        console.log("Bug created:", createdBug);

        // Clear form and close dialog
        this.selectedApp = null;
        this.newBugTitle = '';
        this.newBugDescription = '';
        this.showCreateDialog = false;
      },
      error: (err) => {
        console.error('Failed to create bug:', err);
        alert('Error creating bug. Please try again.');
      }
    });
  }

  submitFixRequest() {
    if (!this.selectedBug) return;

    alert(
      `Fix request submitted for: ${this.selectedBug.title}\nComment: ${this.userComment || 'No comment'}`
    );

    this.selectedBug = null;
    this.userComment = '';
    this.showRequestDialog = false;
  }

  getTotalBugs(): number {
    return this.bugs.length;
  }

  getFixedBugs(): number {
    return this.bugs.filter(b => b.status === 'fixed').length;
  }

  getInProgressBugs(): number {
    return this.bugs.filter(b => b.status === 'in-progress').length;
  }

  getOpenBugs(): number {
    return this.bugs.filter(b => b.status === 'open').length;
  }
}
