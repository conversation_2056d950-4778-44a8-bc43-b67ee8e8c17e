:root {
  --gold-color: #ffd700;
  --purple-color: #8a2be2;
}

.professional-footer {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(87, 87, 87, 0.9) 50%, var(--gold-color) 100%);
  color: white;
  position: relative;
  overflow: hidden;
  margin-top: 4rem;
}

/* Main Footer Section */
.footer-main {
  padding: 3rem 0 2rem;
  position: relative;
  z-index: 2;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-main .footer-container {
  display: grid;
  grid-template-columns: 1fr 4fr 1fr;
  gap: 3rem;
  align-items: start;
}

/* Left Section - Company */
.footer-left {
  display: flex;
  justify-content: flex-start;
}

.company-section {
  display: flex;
  align-items: center;
}

.logo-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.footer-logo {
  height: 70px;
  width: auto;
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
  transition: all 0.3s ease;
}

.footer-logo:hover {
  filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.6));
  transform: scale(1.05);
}

.company-info {
  display: flex;
  flex-direction: column;
}

.company-name {
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  background: linear-gradient(45deg, var(--gold-color), white);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.company-tagline {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
  margin: 0;
  font-style: italic;
}

/* Center Section - Navigation */
.footer-center {
  display: flex;
  justify-content: center;
}

.footer-nav {
  display: flex;
  gap: 3rem;
  width: 100%;
  justify-content: space-between;
}

.nav-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.nav-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #fff;
  position: relative;
}

.nav-title::after {
  content: "";
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #fff, transparent);
}

.nav-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.9rem;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.footer-link:hover {
  color: #fff;
  background: rgba(255, 215, 0, 0.1);
  transform: translateX(3px);
}

.link-icon {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Right Section - Location & Social */
.footer-right {
  display: flex;
  justify-content: flex-end;
}

.location-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.location-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
}

.location-icon {
  font-size: 1.1rem;
  color: var(--gold-color);
}

.location-text {
  font-weight: 500;
}

.social-links {
  display: flex;
  gap: 0.75rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.social-link:hover {
  background: var(--gold-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
  border-color: var(--gold-color);
}

.social-icon {
  font-size: 1rem;
}

/* Bottom Bar */
.footer-bottom {
  border-top: 1px solid rgba(255, 215, 0, 0.2);
  padding: 1.5rem 0;
  position: relative;
  z-index: 2;
}

.bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyright {
  margin: 0;
}

.copyright p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
  margin: 0;
}

.legal-links {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.legal-link {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  font-size: 0.85rem;
  transition: all 0.3s ease;
}

.legal-link:hover {
  color: var(--gold-color);
}

.separator {
  color: rgba(255, 255, 255, 0.4);
  font-size: 0.8rem;
}

/* Background Decoration */
.footer-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--gold-color), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%,
  100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-nav {
    gap: 2rem;
  }

  .footer-main .footer-container {
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .footer-main .footer-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .footer-left,
  .footer-right {
    justify-content: center;
  }

  .location-info {
    align-items: center;
  }

  .footer-nav {
    flex-direction: column;
    gap: 2rem;
  }

  .nav-group {
    align-items: center;
  }

  .nav-links {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  .bottom-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .legal-links {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .professional-footer {
    margin-top: 2rem;
  }

  .footer-main {
    padding: 2rem 0 1.5rem;
  }

  .footer-container {
    padding: 0 1rem;
  }

  .company-name {
    font-size: 1.2rem;
  }

  .nav-links {
    gap: 0.75rem;
  }

  .footer-link {
    font-size: 0.85rem;
  }

  .social-links {
    gap: 0.5rem;
  }

  .social-link {
    width: 32px;
    height: 32px;
  }
}
