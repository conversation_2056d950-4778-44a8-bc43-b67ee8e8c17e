<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2>App Management</h2>
    <button class="btn btn-primary" (click)="openAddAppModal()">
      <i class="bi bi-plus-circle"></i> Add App
    </button>
  </div>

  <!-- Scrollable App List -->
  <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
    <table class="table table-bordered table-hover">
      <thead class="table-light">
        <tr>
          <th>#</th>
          <th>Title</th>
          <th>Description</th>
          <th>Website</th>
          <th>Store</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let app of apps; let i = index">
          <td>{{ (pageNumber - 1) * pageSize + i + 1 }}</td>
          <td>{{ app.title }}</td>
          <td>{{ app.description }}</td>
          <td>
            <a *ngIf="app.websiteUrl" [href]="app.websiteUrl" target="_blank">{{ app.websiteUrl }}</a>
          </td>
          <td>
            <a *ngIf="app.storeUrl" [href]="app.storeUrl" target="_blank">{{ app.storeUrl }}</a>
          </td>
          <td>
            <button class="btn btn-sm btn-warning me-1" (click)="openEditAppModal(app)">
              <i class="bi bi-pencil-square"></i> Edit
            </button>
            <button class="btn btn-sm btn-danger" (click)="deleteApp(app.id)">
              <i class="bi bi-trash"></i> Delete
            </button>
          </td>
        </tr>
        <tr *ngIf="apps.length === 0">
          <td colspan="6" class="text-center text-muted">No apps found.</td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Pagination Summary -->
  <div *ngIf="totalCount > 0" class="d-flex justify-content-between align-items-center mt-3">
    <div class="text-muted">
      Showing
      {{ (pageNumber - 1) * pageSize + 1 }}
      –
      {{
        (pageNumber * pageSize) > totalCount
          ? totalCount
          : (pageNumber * pageSize)
      }}
      of {{ totalCount }} apps
    </div>

    <!-- Pagination -->
    <nav *ngIf="totalPages > 1" aria-label="App list pagination">
      <ul class="pagination pagination-sm mb-0">
        <li class="page-item" [class.disabled]="pageNumber === 1">
          <button
            class="page-link"
            (click)="loadApps(pageNumber - 1)"
            [disabled]="pageNumber === 1"
          >
            Previous
          </button>
        </li>
        <li
          class="page-item"
          *ngFor="let page of totalPagesArray()"
          [class.active]="page === pageNumber"
        >
          <button class="page-link" (click)="loadApps(page)">
            {{ page }}
          </button>
        </li>
        <li class="page-item" [class.disabled]="pageNumber === totalPages">
          <button
            class="page-link"
            (click)="loadApps(pageNumber + 1)"
            [disabled]="pageNumber === totalPages"
          >
            Next
          </button>
        </li>
      </ul>
    </nav>
  </div>

  <!-- Add/Edit App Modal -->
  <div class="modal fade" id="appModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <form (ngSubmit)="saveApp()" #appForm="ngForm">
          <div class="modal-header">
            <h5 class="modal-title">{{ isEditMode ? 'Edit App' : 'Add New App' }}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label for="title" class="form-label">Title *</label>
              <input
                type="text"
                id="title"
                class="form-control"
                [(ngModel)]="currentApp.title"
                name="title"
                required
              />
              <div *ngIf="appForm.submitted && !currentApp.title" class="text-danger">
                Title is required.
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">Description</label>
              <textarea
                class="form-control"
                [(ngModel)]="currentApp.description"
                name="description"
                rows="3"
              ></textarea>
            </div>
            <div class="mb-3">
              <label class="form-label">Logo URL</label>
              <input
                type="url"
                class="form-control"
                [(ngModel)]="currentApp.logoUrl"
                name="logoUrl"
              />
            </div>
            <div class="mb-3">
              <label class="form-label">Website URL</label>
              <input
                type="url"
                class="form-control"
                [(ngModel)]="currentApp.websiteUrl"
                name="websiteUrl"
              />
            </div>
            <div class="mb-3">
              <label class="form-label">Store URL</label>
              <input
                type="url"
                class="form-control"
                [(ngModel)]="currentApp.storeUrl"
                name="storeUrl"
              />
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              Cancel
            </button>
            <button type="submit" class="btn btn-primary" [disabled]="!appForm.valid">
              {{ isEditMode ? 'Update App' : 'Add App' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
