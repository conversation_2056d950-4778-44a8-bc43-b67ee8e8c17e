import { Component } from '@angular/core';
import { PageTitleService } from '../../Utils/_services/page-title.service';
import { BugReportingService } from '../../services/bug-reporting.service';
import { JobService } from '../../services/job.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css'
})
export class DashboardComponent {

   data = {
    jobs: 0,
    users: 0,
    blogs: 0,
    bugs: 0
  };
 constructor( private pageTitle: PageTitleService,
  private bugapi : BugReportingService,
  private jobapi: JobService
 ){
 }

  ngOnInit(): void {
  this.pageTitle.setTitle('Dashboard');
  this.loadBugCount()
  this.loadJobCount()
 }

   loadBugCount() {
    this.bugapi.getAll(1, 1).subscribe({
      next: (result) => {
        this.data.bugs = result.totalCount; // Set totalCount to bugs
      },
      error: (err) => {
        console.error('Failed to load bug count:', err);
      }
    });
  }

    loadJobCount() {
    this.jobapi.getJobs().subscribe({
      next: (result) => {
        this.data.jobs = result.length; // Set totalCount to jobs
      },
      error: (err) => {
        console.error('Failed to load job count:', err);
      }
    });
  }

}
