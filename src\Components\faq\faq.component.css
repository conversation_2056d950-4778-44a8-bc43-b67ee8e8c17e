:root {
  --gold-color: #ffd700;
  --purple-color: #8a2be2;
}

.faq-page {
  min-height: 100vh;
}

/* Hero Section */
.faq-hero {
  position: relative;
  min-height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    var(--gold-color) 0%,
    rgba(255, 215, 0, 0.8) 25%,
    rgba(87, 87, 87, 0.9) 75%,
    rgba(0, 0, 0, 0.95) 100%
  );
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 215, 0, 0.1);
  animation: float 8s ease-in-out infinite;
}

.circle-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.circle-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 20%;
  animation-delay: 3s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 70%;
  animation-delay: 6s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(180deg);
  }
}

.hero-content {
  text-align: center;
  z-index: 2;
  max-width: 800px;
  padding: 80px 0;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.5);
  border-radius: 30px;
  padding: 12px 24px;
  margin-bottom: 1rem;
  color: white;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.badge-icon {
  font-size: 1.2rem;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  color: white;
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.title-word {
  display: inline-block;
  animation: titleSlide 1s ease-out forwards;
  opacity: 0;
  transform: translateY(50px);
}


@keyframes titleSlide {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

/* FAQ Content */
.faq-content {
  padding: 4rem 0;
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
}

.content-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 2rem;
}

.faq-container {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

/* FAQ Header */
.faq-header {
  text-align: center;
  margin-bottom: 2rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 1rem;
}

.section-subtitle {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

/* FAQ List */
.faq-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.faq-item {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.faq-item:nth-child(1) {
  animation-delay: 0.1s;
}
.faq-item:nth-child(2) {
  animation-delay: 0.2s;
}
.faq-item:nth-child(3) {
  animation-delay: 0.3s;
}
.faq-item:nth-child(4) {
  animation-delay: 0.4s;
}
.faq-item:nth-child(5) {
  animation-delay: 0.5s;
}
.faq-item:nth-child(6) {
  animation-delay: 0.6s;
}
.faq-item:nth-child(7) {
  animation-delay: 0.7s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.faq-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 1.5rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.faq-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(45deg, var(--gold-color), var(--purple-color));
  transition: width 0.3s ease;
}

.faq-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
  border-color: var(--gold-color);
}

.faq-card:hover::before {
  width: 8px;
}

.faq-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, var(--gold-color), var(--purple-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.faq-content-wrapper {
  flex: 1;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.faq-question h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.4;
  flex: 1;
}

.question-icon {
  margin-left: 1rem;
  font-size: 1.5rem;
  opacity: 0.7;
  flex-shrink: 0;
}

.faq-answer {
  padding-left: 0;
}

.faq-answer p {
  color: #666;
  line-height: 1.6;
  margin: 0;
  font-size: 1rem;
}

/* Support Section */
.support-section {
  margin-top: 3rem;
}

.support-card {
  background: linear-gradient(135deg, var(--gold-color), var(--purple-color));
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.support-card::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: rotate 10s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.support-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
}

.support-content {
  position: relative;
  z-index: 2;
}

.support-content h3 {
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.support-content p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.support-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: white;
  color: var(--purple-color);
  border: none;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.support-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  background: #f8f9fa;
}

.btn-icon {
  font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .faq-card {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .faq-question {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .question-icon {
    margin-left: 0;
  }

  .support-card {
    padding: 2rem;
  }

  .section-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .content-container {
    padding: 0 1rem;
  }

  .faq-card {
    padding: 1.5rem;
  }

  .hero-content {
    padding: 1rem;
  }

  .faq-number {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .faq-question h3 {
    font-size: 1.1rem;
  }
}
