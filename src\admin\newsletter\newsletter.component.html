<div class="container-fluid">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2 class="mb-1 fw-bold">Newsletter Subscribers</h2>
      <p class="text-muted mb-0">View users who have subscribed to the newsletter</p>
    </div>
    <div class="d-flex gap-2 align-items-center">
      <select
        class="form-select"
        style="width: auto;"
        [(ngModel)]="pageSize"
        (change)="onPageSizeChange()">
        <option value="5">5 per page</option>
        <option value="10">10 per page</option>
        <option value="25">25 per page</option>
        <option value="50">50 per page</option>
      </select>
    </div>
  </div>

  <div *ngIf="isLoading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2 text-muted">Loading subscribers...</p>
  </div>

  <div *ngIf="errorMessage && !isLoading" class="alert alert-danger" role="alert">
    <i class="bi bi-exclamation-triangle me-2"></i>
    {{ errorMessage }}
    <button class="btn btn-sm btn-outline-danger ms-2" (click)="loadSubscribers()">
      <i class="bi bi-arrow-clockwise me-1"></i>Retry
    </button>
  </div>

  <div *ngIf="!isLoading && !errorMessage">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <div class="text-muted">
        <small>
          Showing {{ getStartIndex() }} to {{ getEndIndex() }} of {{ totalCount }} subscribers
        </small>
      </div>
      <div class="badge bg-success fs-6">
        <i class="bi bi-envelope me-1"></i>
        {{ totalCount }} Total Subscribers
      </div>
    </div>

    <div class="table-responsive">
      <table class="table table-hover align-middle">
        <thead class="table-light">
          <tr>
            <th scope="col">#</th>
            <th scope="col">Subscriber</th>
            <th scope="col">Email</th>
            <th scope="col">Role</th>
            <th scope="col">Status</th>
            <th scope="col">Subscribed Date</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let subscriber of subscribers; let i = index">
            <td>{{ getStartIndex() + i }}</td>
            <td>
              <div class="d-flex align-items-center">
                <div class="avatar-circle me-2">
                  {{ (subscriber.firstName || subscriber.userName)?.charAt(0)?.toUpperCase() || 'U' }}
                </div>
                <div>
                  <div class="fw-semibold">{{ subscriber.firstName || 'N/A' }} {{ subscriber.lastName || '' }}</div>
                  <small class="text-muted">{{ '@' + (subscriber.userName || 'N/A') }}</small>
                </div>
              </div>
            </td>
            <td>{{ subscriber.email || 'N/A' }}</td>
            <td>{{ subscriber.role || 'N/A' }}</td>
            <td>
              <span class="badge" [class.bg-success]="subscriber.isActive" [class.bg-danger]="!subscriber.isActive">
                {{ subscriber.isActive ? 'Active' : 'Inactive' }}
              </span>
            </td>
            <td>{{ subscriber.createdAt ? formatDate(subscriber.createdAt) : 'N/A' }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div *ngIf="subscribers.length === 0" class="text-center py-5">
      <i class="bi bi-envelope-x display-1 text-muted"></i>
      <h4 class="mt-3">No Newsletter Subscribers</h4>
      <p class="text-muted">No users have subscribed to the newsletter yet.</p>
    </div>

    <nav *ngIf="totalPages > 1" aria-label="Subscribers pagination" class="mt-4">
      <ul class="pagination justify-content-center">
        <li class="page-item" [class.disabled]="pageNumber === 1">
          <button class="page-link" (click)="prevPage()" [disabled]="pageNumber === 1">
            <i class="bi bi-chevron-left"></i>
            Previous
          </button>
        </li>

        <li *ngFor="let page of getPaginationArray()"
            class="page-item"
            [class.active]="page === pageNumber">
          <button class="page-link" (click)="goToPage(page)">
            {{ page }}
          </button>
        </li>

        <li class="page-item" [class.disabled]="pageNumber === totalPages">
          <button class="page-link" (click)="nextPage()" [disabled]="pageNumber === totalPages">
            Next
            <i class="bi bi-chevron-right"></i>
          </button>
        </li>
      </ul>
    </nav>
  </div>
</div>
