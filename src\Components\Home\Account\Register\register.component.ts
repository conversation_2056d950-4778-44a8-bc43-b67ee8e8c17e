import { Component, OnInit, ViewChild, afterNextRender, DestroyRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm } from '@angular/forms';
import { AccountService } from "../../../../Utils/_services/account.service";
import { ToastrService } from 'ngx-toastr';
import { IRegisterUser_Request } from "./register.component.model";
import { debounceTime } from 'rxjs';
import { Router } from '@angular/router';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [FormsModule, CommonModule],
  templateUrl: './register.component.html',
  styleUrl: './register.component.css'
})
export class RegisterComponent implements OnInit {
  @ViewChild('registerForm') registerForm?: NgForm;
  model: any = {};
  private _destroyRef: DestroyRef;

  constructor(
    private accountService: AccountService,
    private toastr: ToastrService,
    destroyRef: DestroyRef,
    private router: Router
  ) {
    this._destroyRef = destroyRef;
  }

  ngOnInit(): void {
    afterNextRender(() => {
      const savedForm = window.localStorage.getItem("saved-register-form");
      if (savedForm) {
        this.model = JSON.parse(savedForm);
      }

      const subscription = this.registerForm?.valueChanges?.pipe(
        debounceTime(500)
      ).subscribe(value => {
        window.localStorage.setItem("saved-register-form", JSON.stringify(value));
      });

      if (subscription) {
        this._destroyRef.onDestroy(() => subscription.unsubscribe());
      }
    });
  }

  onSubmitForm(): void {
    if (this.registerForm?.invalid) {
      this.toastr.error("Please fill all required fields correctly.");
      this.registerForm?.form.markAllAsTouched();
      return;
    }

    const registerRequest: IRegisterUser_Request = {
      firstName: this.model.firstName,
      lastName: this.model.lastName,
      email: this.model.email,
      password: this.model.password,
      username: this.model.username, 
      dob: this.model.dob,
      newsletter: this.model.newsletter
    };

    this.accountService.onSubmitForm(registerRequest).subscribe({
      next: () => {
        this.toastr.success("Registration successful!");
        this.registerForm?.reset();
        window.localStorage.removeItem("saved-register-form");
        this.cancel();
        this.router.navigate(['/login']); 
      },
      error: error => {
        this.toastr.error(error?.error?.message || "Registration failed.");
      }
    });
  }

  cancel(): void {
    console.log("Registration process finished or cancelled.");
  }
}
