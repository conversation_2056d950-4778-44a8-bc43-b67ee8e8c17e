<div class="login-container">
  <div class="login-card">
    <!-- Header -->
    <div class="login-header">
      <div class="logo-section">
        <i class="bi bi-shield-lock-fill logo-icon"></i>
        <h1 class="login-title">Welcome Back</h1>
        <p class="login-subtitle">Sign in to your account</p>
      </div>
    </div>

    <!-- Error Message -->
    <div *ngIf="errorMessage" class="alert alert-danger" role="alert">
      <i class="bi bi-exclamation-triangle me-2"></i>
      {{ errorMessage }}
      <button type="button" class="btn-close" (click)="clearError()"></button>
    </div>

    <!-- Login Form -->
    <form (ngSubmit)="onSubmit()" #loginForm="ngForm" class="login-form">
      <!-- Username/Email Field -->
      <div class="form-group">
        <label for="usernameOrEmail" class="form-label">
          <i class="bi bi-person me-2"></i>Username or Email
        </label>
        <input
          type="text"
          id="usernameOrEmail"
          name="usernameOrEmail"
          class="form-control"
          [(ngModel)]="loginModel.usernameOrEmail"
          required
          placeholder="Enter your username or email"
          [disabled]="isLoading"
          #usernameOrEmail="ngModel"
        />
        <div *ngIf="usernameOrEmail.invalid && (usernameOrEmail.dirty || usernameOrEmail.touched)" class="error-message">
          <small *ngIf="usernameOrEmail.errors?.['required']">Username or email is required.</small>
        </div>
      </div>

      <!-- Password Field -->
      <div class="form-group">
        <label for="password" class="form-label">
          <i class="bi bi-lock me-2"></i>Password
        </label>
        <div class="password-input-group">
          <input
            [type]="showPassword ? 'text' : 'password'"
            id="password"
            name="password"
            class="form-control"
            [(ngModel)]="loginModel.password"
            required
            placeholder="Enter your password"
            [disabled]="isLoading"
            #password="ngModel"
          />
          <button
            type="button"
            class="password-toggle-btn"
            (click)="togglePasswordVisibility()"
            [disabled]="isLoading"
          >
            <i class="bi" [ngClass]="showPassword ? 'bi-eye-slash' : 'bi-eye'"></i>
          </button>
        </div>
        <div *ngIf="password.invalid && (password.dirty || password.touched)" class="error-message">
          <small *ngIf="password.errors?.['required']">Password is required.</small>
        </div>
      </div>

      <!-- Remember Me & Forgot Password -->
      <div class="form-options">
        <div class="remember-me">
          <input
            type="checkbox"
            id="rememberMe"
            name="rememberMe"
            [(ngModel)]="rememberMe"
            [disabled]="isLoading"
          />
          <label for="rememberMe" class="remember-label">Remember me</label>
        </div>
        <button
          type="button"
          class="forgot-password-link"
          (click)="navigateToForgotPassword()"
          [disabled]="isLoading"
        >
          Forgot password?
        </button>
      </div>

      <!-- Submit Button -->
      <button
        type="submit"
        class="login-btn"
        [disabled]="loginForm.invalid || isLoading"
      >
        <span *ngIf="!isLoading">
          <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
        </span>
        <span *ngIf="isLoading" class="loading-content">
          <i class="bi bi-arrow-repeat spin me-2"></i>Signing In...
        </span>
      </button>
    </form>

    <!-- Divider -->
    <div class="divider">
      <span class="divider-text">or</span>
    </div>

    <!-- Register Link -->
    <div class="register-section">
      <p class="register-text">
        Don't have an account?
        <button
          type="button"
          class="register-link"
          (click)="navigateToRegister()"
          [disabled]="isLoading"
        >
          Create one here
        </button>
      </p>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="background-elements">
    <div class="bg-circle bg-circle-1"></div>
    <div class="bg-circle bg-circle-2"></div>
    <div class="bg-circle bg-circle-3"></div>
  </div>
</div>
